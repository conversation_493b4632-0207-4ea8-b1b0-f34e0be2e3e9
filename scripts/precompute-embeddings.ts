import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import { DocumentProcessor } from '../src/lib/rag/documentProcessor';
import { EmbeddingService } from '../src/lib/rag/embeddingService';
import { RAGDocument } from '../src/lib/rag/types';

// You can add a simple check to ensure --expose-gc was effective:
if (typeof global.gc !== 'function') {
    console.warn(
        "⚠️ Warning: global.gc is not available. " +
        "Memory cleanup calls will be ineffective. " +
        "Ensure your script is run with Node's --expose-gc flag (e.g., via tsx --expose-gc ...)."
    );
}

const MEMORY_LIMIT_MB = 2024; // You might still want this constant for other logic, like in forceCleanup

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

function getMemoryUsage() {
    const used = process.memoryUsage();
    return {
        rss: Math.round(used.rss / 1024 / 1024 * 100) / 100,
        heapUsed: Math.round(used.heapUsed / 1024 / 1024 * 100) / 100,
        heapTotal: Math.round(used.heapTotal / 1024 / 1024 * 100) / 100,
        external: Math.round(used.external / 1024 / 1024 * 100) / 100
    };
}

async function forceCleanup(level = "normal") { // Added level for more control
    if (global.gc) {
        const memoryBefore = getMemoryUsage();
        // Force multiple GC cycles
        for (let i = 0; i < (level === "aggressive" ? 3 : 1); i++) { // Less aggressive by default
            global.gc();
            // Only wait if being aggressive, otherwise, let Node decide timing
            if (level === "aggressive") await new Promise(resolve => setTimeout(resolve, 50));
        }

        const memoryAfter = getMemoryUsage();
        console.log(`🧹 Memory after cleanup (level: ${level}) - Heap: ${memoryAfter.heapUsed}MB/${memoryAfter.heapTotal}MB (Δ ${Math.round((memoryAfter.heapUsed - memoryBefore.heapUsed) * 100) / 100}MB), RSS: ${memoryAfter.rss}MB`);

        // Emergency exit if memory is too high
        if (memoryAfter.heapUsed > MEMORY_LIMIT_MB * 0.8) { // Check against 80% of the limit
            console.error(`💥 Emergency: Memory too high (${memoryAfter.heapUsed}MB) after cleanup`);
            process.exit(1);
        }
    }
}

async function clearDatabase() {
    console.log("🧹 Clearing database...");
    const { error } = await supabase
        .from('document_embeddings')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Keep a dummy row if needed or use .gt('id', 0) if id is int
    if (error) {
        console.error("Database clear failed:", error);
        throw error;
    }
    console.log("✅ Database cleared");
}

async function processAndStoreChunk(chunk: RAGDocument, embeddingService: EmbeddingService) {
    try {
        // console.log(`    Processing chunk: ${chunk.id}`); // Can be too verbose
        if (!chunk.content || chunk.content.trim().length === 0) {
            console.log(`    Skipping empty chunk: ${chunk.id}`);
            return false;
        }

        const embedding = await embeddingService.generateEmbedding(chunk.content);
        if (!embedding || embedding.length === 0) {
            console.warn(`    ⚠️ Failed to generate embedding or got empty embedding for chunk: ${chunk.id}`);
            return false;
        }

        const { error } = await supabase
            .from('document_embeddings')
            .insert({
                document_id: chunk.id, // This is actually chunk_id
                content: chunk.content,
                metadata: chunk.metadata, // Contains parent_id
                embedding: `[${embedding.join(',')}]`
            });

        if (error) {
            console.error(`Error storing chunk ${chunk.id}:`, error);
            // Consider more specific error handling, e.g., for duplicate primary key
            return false;
        }
        // console.log(`    ✅ Stored embedding for ${chunk.id}`); // Can be too verbose
        return true;
    } catch (error) {
        console.error(`Error processing chunk ${chunk.id}:`, error);
        return false;
    }
}

async function processDirectoryDocuments() {
    console.log("🚀 Starting directory-by-directory processing...");
    console.log(`🔋 Initial memory - ${JSON.stringify(getMemoryUsage())}`);

    await clearDatabase();

    const processor = new DocumentProcessor();
    const embeddingService = new EmbeddingService();

    console.log("🔄 Initializing embedding service (this may take a moment)...");
    await embeddingService.initialize();
    console.log("✅ Embedding service initialized and ready");

    let totalChunksProcessed = 0;
    let totalFilesProcessed = 0;
    let totalEmptyDocsSkipped = 0;

    const directories = await processor.getDirectoryList();
    console.log(`📁 Found ${directories.length} directories to process:`, directories);

    const FILES_PER_BATCH_FOR_CLEANUP = 10; // Process 10 files, then do a more thorough cleanup

    for (let dirIndex = 0; dirIndex < directories.length; dirIndex++) {
        const directoryName = directories[dirIndex];
        console.log(`\n📁 Processing directory ${dirIndex + 1}/${directories.length}: ${directoryName}`);

        const filePaths = await processor.getFilePathsInDirectory(directoryName);
        console.log(`  Found ${filePaths.length} files to process in ${directoryName}.`);

        if (filePaths.length === 0) {
            console.log(`  No files to process in ${directoryName}. Skipping.`);
            continue;
        }

        for (let fileIndex = 0; fileIndex < filePaths.length; fileIndex++) {
            const filePath = filePaths[fileIndex];
            // Assign to a block-scoped variable to help GC
            let doc: RAGDocument | null = await processor.loadSingleDocumentByPath(filePath, directoryName);

            if (!doc) {
                console.warn(`    ⚠️ Failed to load document: ${filePath}. Skipping.`);
                continue;
            }
            totalFilesProcessed++;
            console.log(`  📄 Processing file ${fileIndex + 1}/${filePaths.length}: ${doc.metadata.filename} (ID: ${doc.id})`);

            const memoryBeforeDoc = getMemoryUsage();
            // console.log(`  💾 Memory before document - Heap: ${memoryBeforeDoc.heapUsed}MB, RSS: ${memoryBeforeDoc.rss}MB`);

            if (memoryBeforeDoc.heapUsed > MEMORY_LIMIT_MB * 0.65) { // More conservative threshold
                console.log("  🚨 Pre-emptive memory cleanup before document processing");
                await forceCleanup("aggressive");
            }

            if (!doc.content || doc.content.trim().length === 0) {
                console.log(`    Skipping empty document content: ${doc.id}`);
                totalEmptyDocsSkipped++;
                doc.content = ''; // Help GC
                doc = null; // Help GC
                continue;
            }

            let docChunksProcessed = 0;
            if (doc.metadata.filename.toLowerCase().endsWith('.sql')) {
                console.log('    📝 SQL file detected - splitting by statements');
                const statements = doc.content.split(';').filter(s => s.trim().length > 0);
                // console.log(`    📊 Found ${statements.length} SQL statements`);

                for (let j = 0; j < statements.length; j++) {
                    const statement = statements[j].trim();
                    if (statement.length === 0) continue;

                    // Create a RAGDocument for the SQL statement chunk
                    const sqlChunkDoc: RAGDocument = {
                        id: `${doc.id}_stmt_${j}`, // Unique ID for the statement chunk
                        content: statement,
                        metadata: {
                            ...doc.metadata, // Inherit metadata from parent doc
                            parent_id: doc.id,
                            chunk_index: j,
                            is_sql_statement: true,
                            original_length: doc.content.length,
                        },
                    };
                    // console.log(`Processing SQL chunk: ${sqlChunkDoc.id}`)
                    const success = await processAndStoreChunk(sqlChunkDoc, embeddingService);
                    if (success) {
                        totalChunksProcessed++;
                        docChunksProcessed++;
                    }
                    // Minimal GC after each small SQL statement chunk
                    if (global.gc) global.gc();
                }
            } else {
                // Regular file processing (non-SQL)
                const chunks: RAGDocument[] = processor.chunkDocument(doc);
                // console.log(`    Generated ${chunks.length} chunks for ${doc.metadata.filename}`);

                for (let c = 0; c < chunks.length; c++) {
                    // console.log(`Processing chunk ${c+1}/${chunks.length} from ${doc.metadata.filename}`)
                    const success = await processAndStoreChunk(chunks[c], embeddingService);
                    if (success) {
                        totalChunksProcessed++;
                        docChunksProcessed++;
                    }
                    chunks[c].content = ''; // Help GC
                    chunks[c] = null as any; // Help GC
                    // Minimal GC after each chunk
                    if (global.gc) global.gc();
                }
            }
            console.log(`    Processed ${docChunksProcessed} chunks for ${doc.metadata.filename}`);

            doc.content = ''; // Explicitly clear content to help GC
            doc = null; // Explicitly nullify to help GC

            // More thorough cleanup periodically or if memory is high
            if ((fileIndex + 1) % FILES_PER_BATCH_FOR_CLEANUP === 0 || memoryBeforeDoc.heapUsed > MEMORY_LIMIT_MB * 0.5) {
                // console.log(`  Performing cleanup after processing file #${fileIndex + 1}`);
                await forceCleanup("normal");
            }
        }
        console.log(`  ✅ Completed directory ${directoryName}. Processed ${filePaths.length} files.`);
        await forceCleanup("aggressive"); // Aggressive cleanup after each directory
    }

    console.log("🔄 Disposing embedding service...");
    embeddingService.dispose(); // Ensure this properly releases any models/resources
    console.log("✅ Embedding service disposed.");

    const { count, error: countError } = await supabase
        .from('document_embeddings')
        .select('*', { count: 'exact', head: true });

    if (countError) console.error("Error fetching final count:", countError);

    console.log(`\n🎉 Completed! Total embeddings stored in DB: ${count || 0}`);
    console.log(`📊 Processed ${totalChunksProcessed} chunks from ${totalFilesProcessed} files across ${directories.length} directories.`);
    console.log(`⚠️  Skipped ${totalEmptyDocsSkipped} empty documents/contents.`);
    console.log(`🔋 Final memory - ${JSON.stringify(getMemoryUsage())}`);

    await forceCleanup("aggressive");
}

// Enhanced error handling
process.on('uncaughtException', (error, origin) => {
    console.error('💥 Uncaught Exception:', error, 'Origin:', origin);
    console.error(`Memory at crash: ${JSON.stringify(getMemoryUsage())}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    console.error(`Memory at rejection: ${JSON.stringify(getMemoryUsage())}`);
    process.exit(1);
});

processDirectoryDocuments().catch((error) => {
    console.error("❌ Script failed:", error);
    console.error(`Memory at failure: ${JSON.stringify(getMemoryUsage())}`);
    process.exit(1);
});