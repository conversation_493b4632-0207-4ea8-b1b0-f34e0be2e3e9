import { PageLayout } from "@/components/PageLayout";
import { SITE_CONFIG } from "@/lib/constants";
import { ExternalLink, Github, Calendar, Code, Database, Zap, BarChart3, Brain, GraduationCap } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const projects = [
  {
    id: "gengo",
    title: "Gengo - Relational Data Generator",
    shortDescription: "High-performance CLI tool for generating large-scale synthetic relational datasets",
    description: "Gengo is a command-line tool written in Go for rapidly generating large, synthetic relational datasets (dimension and fact tables). Built to address the performance limitations of scripting languages when generating millions of rows, leveraging Go's concurrency for significant speed improvements.",
    technologies: ["Golang", "CLI", "Apache Parquet", "Concurrent Processing", "3NF Data Modeling"],
    githubUrl: "https://github.com/peekknuf/Gengo-Large-Volume-Data-Generation",
    features: [
      "Fast data generation using Go's performance and concurrency",
      "Predefined 3NF e-commerce data model (customers, products, locations, orders)",
      "Multiple output formats: CSV, JSON Lines, compressed Parquet",
      "Weighted sampling for realistic purchasing patterns",
      "Size-based input with automatic row count estimation",
      "Interactive CLI with customizable schema and generation logic"
    ],
    highlights: [
      "10x performance improvement over Python prototype",
      "Handles millions of rows with foreign key relationships",
      "Compressed Parquet output with Snappy for efficient storage"
    ],
    icon: Database
  },
  {
    id: "clarity-log-parser",
    title: "Clarity - Log Parsing CLI/TUI",
    shortDescription: "Python tool for analyzing connection logs with dual-interface architecture",
    description: "A comprehensive log analysis tool featuring both Terminal User Interface (TUI) and Command Line Interface (CLI) modes. Designed for analyzing connection logs with real-time monitoring capabilities and flexible time-based filtering.",
    technologies: ["Python", "CLI", "TUI (Textual)", "Real-time Monitoring", "Log Analysis"],
    githubUrl: "https://github.com/peekknuf/Clarity-Log-Parsing-CLI",
    features: [
      "Dual-interface: Interactive TUI and scriptable CLI modes",
      "Real-time directory monitoring with file change detection",
      "Batch processing with flexible time range filtering",
      "Connection statistics and activity reporting",
      "Keyboard navigation and user-friendly terminal interface",
      "Modular architecture for maintainability"
    ],
    highlights: [
      "Real-time stream processing for security log analysis",
      "Handles file truncation and incremental log processing",
      "Responsive terminal interface with textual library"
    ],
    icon: Code
  },
  {
    id: "vendor-analysis",
    title: "Vendor Analysis & Optimization",
    shortDescription: "AI-powered vendor spend analysis with strategic cost-saving recommendations",
    description: "Simulated acquisition analyst role to evaluate and optimize vendor spend for a recently acquired company. Analyzed ~400 vendors using AI automation to provide strategic recommendations aligned with remote-first, lean operational models.",
    technologies: ["Google Apps Script", "Google Sheets", "Gemini API", "AI/ML Integration"],
    githubUrl: "https://github.com/peekknuf/VendorAnalysis",
    features: [
      "Automated vendor categorization and analysis workflow",
      "AI-powered department assignment and strategic recommendations",
      "Multi-phase processing pipeline with distinct AI prompts",
      "Structured JSON output parsing and integration",
      "Custom spreadsheet functions and user interface"
    ],
    highlights: [
      "Processed ~400 vendors with automated AI analysis",
      "Quantified strategic cost-saving opportunities",
      "Robust rate limiting and error handling for AI API calls"
    ],
    icon: BarChart3
  },
  {
    id: "genai-email",
    title: "GenAI Email Processing System",
    shortDescription: "Intelligent email processing with RAG-enhanced contextual understanding",
    description: "Engineered intelligent system for automated email classification and order processing using GenAI and RAG techniques. Eliminates manual email processing overhead while providing enhanced contextual understanding for fashion store operations.",
    technologies: ["Python", "OpenAI API", "Langchain", "RAG", "FAISS", "Google Sheets API"],
    githubUrl: "https://github.com/peekknuf/Generative-AI-emails",
    features: [
      "Automated email classification (product inquiry vs order request)",
      "RAG implementation for product catalog queries using FAISS vector store",
      "Fuzzy matching for product name recognition and stock verification",
      "Dynamic stock level updates and order fulfillment tracking",
      "Professional email response generation with customer-friendly tone",
      "Structured Excel output with organized result sheets"
    ],
    highlights: [
      "Eliminated manual email processing overhead",
      "Advanced RAG with all-MiniLM-L6-v2 embeddings for scalable product queries",
      "Production-ready error handling and API integration"
    ],
    icon: Brain
  },
  {
    id: "loc-sync",
    title: "Localization Engineering Sync Tool",
    shortDescription: "Data synchronization between Google Sheets and Gridly for localization workflows",
    description: "A focused synchronization tool for coordinating data between Google Sheets and Gridly accounts, designed for small to medium localization batches. Features automated cron-based scheduling for regular sync operations.",
    technologies: ["Python", "Google Sheets API", "Gridly API", "Cron Jobs", "Data Sync"],
    githubUrl: "https://github.com/peekknuf/Localization-Engineering",
    features: [
      "Bidirectional sync between Google Sheets and Gridly",
      "Support for multiple spreadsheets and worksheets",
      "Automated daily synchronization via cron jobs",
      "Comprehensive error handling for failed syncs",
      "Granular control over specific views and worksheets"
    ],
    highlights: [
      "Optimized for small-scale localization workflows",
      "Automated daily sync at 6 PM via cron",
      "Custom implementation challenging established SDK patterns"
    ],
    icon: Database
  },
  {
    id: "portfolio-website",
    title: "Interactive Portfolio Website",
    shortDescription: "This very website with AI-powered chat and RAG system",
    description: "Interactive portfolio website featuring AI-powered chat functionality with a comprehensive RAG (Retrieval-Augmented Generation) system. Provides personalized responses about background, projects, and technical expertise through intelligent document processing.",
    technologies: ["React", "TypeScript", "Vite", "Tailwind CSS", "Google Gemini API", "RAG", "Transformers.js", "shadcn/ui"],
    githubUrl: "#",
    features: [
      "AI-powered chat interface with contextual responses",
      "RAG system for retrieving relevant information",
      "Responsive design with dark/light theme support",
      "Real-time document processing and vector search",
      "Professional portfolio presentation with project showcase"
    ],
    highlights: [
      "Advanced RAG implementation for personalized interactions",
      "Seamless integration of AI chat with portfolio content",
      "Modern React architecture with TypeScript"
    ],
    icon: Code,
    isCurrentSite: true
  },
  {
    id: "dataexpert-bootcamp",
    title: "DataExpert.io Data Engineering Bootcamp",
    shortDescription: "Comprehensive data engineering training with dimensional modeling, Spark, and real-time processing",
    description: "Intensive hands-on bootcamp covering advanced data engineering concepts including dimensional data modeling, fact data modeling, Spark & Iceberg implementation, and real-time stream processing with Flink. Developed production-ready data pipelines and analytics systems.",
    technologies: ["SQL", "PySpark", "Apache Spark", "Apache Iceberg", "Apache Flink", "Kafka", "PostgreSQL", "Dimensional Modeling", "ETL/ELT"],
    githubUrl: "https://github.com/DataExpert-io",
    features: [
      "Dimensional data modeling with SCD (Slowly Changing Dimensions) management",
      "Fact data modeling with deduplication and incremental loading strategies",
      "Advanced Spark configurations with bucketing and join optimization",
      "Real-time sessionization using Flink with 5-minute session gaps",
      "Kafka integration for stream processing and event-time handling",
      "PostgreSQL sink implementations for both batch and streaming data"
    ],
    highlights: [
      "Designed robust SQL scripts for historical data tracking with advanced window functions",
      "Implemented efficient deduplication using row_number() and partitioning strategies",
      "Achieved 10x performance improvement through Go's concurrency in related data generation tools",
      "Built fault-tolerant Flink jobs with checkpointing and watermarking for out-of-order events"
    ],
    accomplishments: {
      "Dimensional Data Modeling": [
        "Designed and implemented robust SQL scripts for managing historical actors' data",
        "Developed custom types, CTEs, and logical primary keys for data integrity",
        "Applied advanced SQL techniques like window functions and change indicators",
        "Implemented SCD management with temporal consistency through logical record separation"
      ],
      "Fact Data Modeling": [
        "Created efficient deduplication scripts using row_number() and partitioning",
        "Implemented date list to integer transformation using bit manipulation",
        "Built incremental loading strategies for cumulative data aggregation",
        "Designed schemas with proper primary key definitions for data integrity"
      ],
      "Spark & Iceberg": [
        "Configured advanced PySpark settings for join strategy optimization",
        "Implemented bucket joins with 16 buckets for distributed processing efficiency",
        "Executed complex aggregation queries for analytics and reporting",
        "Optimized data size through partitioning and sorting strategies"
      ],
      "Flink Sessionization": [
        "Built real-time sessionization job processing web traffic by IP and host",
        "Implemented 5-minute session gaps using Flink's Session API",
        "Integrated Kafka source with proper watermarking for event-time processing",
        "Created PostgreSQL sinks for both sessionized data and computed metrics"
      ]
    },
    icon: GraduationCap
  }
];

export default function Projects() {
  return (
    <PageLayout currentPage="projects">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">My Projects</h1>
        <div className="flex items-center gap-2 text-zinc-500 dark:text-zinc-500 text-sm">
          <Calendar className="h-4 w-4" />
          May 17, 2025 · 6 min · {SITE_CONFIG.name}
        </div>
      </div>

      {/* Introduction */}
      <div className="mb-8">
        <p className="text-zinc-700 dark:text-zinc-300 leading-relaxed text-lg">
          A collection of my engineering projects showcasing expertise in data engineering,
          system design, AI integration, and automation. Each project demonstrates practical
          problem-solving with measurable impact and modern technologies.
        </p>
      </div>

      {/* Table of Contents */}
      <Card className="mb-8 bg-sidebar-background border border-sidebar-border">
        <CardHeader>
          <CardTitle className="text-lg text-foreground">Project Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {projects.map((project) => (
              <a
                key={project.id}
                href={`#${project.id}`}
                className="flex items-center gap-2 text-sidebar-foreground hover:text-foreground text-sm p-2 rounded hover:bg-sidebar-accent transition-colors"
              >
                <project.icon className="h-4 w-4 flex-shrink-0 text-sidebar-foreground" />
                <span className="truncate">{project.title}</span>
              </a>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Projects Grid - Two Columns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {projects.map((project) => (
          <Card
            key={project.id}
            id={project.id}
            className="hover:shadow-lg transition-shadow bg-sidebar-accent border border-sidebar-border"
          >
            <CardHeader>
              <div className="flex items-start justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-sidebar-primary">
                    <project.icon className="h-6 w-6 text-sidebar-accent-foreground" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-foreground">{project.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {project.shortDescription}
                    </CardDescription>
                  </div>
                </div>
                <a
                  href={project.githubUrl}
                  className="inline-flex items-center gap-2 px-3 py-1.5 text-sm border border-sidebar-border rounded-lg hover:bg-sidebar-background transition-colors flex-shrink-0 text-sidebar-foreground"
                  target={project.isCurrentSite ? "_self" : "_blank"}
                  rel={project.isCurrentSite ? "" : "noopener noreferrer"}
                >
                  {project.isCurrentSite ? <Code className="h-4 w-4" /> : <Github className="h-4 w-4" />}
                  {project.isCurrentSite ? "View Code" : "GitHub"}
                  {!project.isCurrentSite && <ExternalLink className="h-3 w-3" />}
                </a>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Description */}
              <p className="text-zinc-700 dark:text-zinc-300 leading-relaxed">
                {project.description}
              </p>

              {/* Technologies */}
              <div>
                <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-2">
                  Technologies
                </h4>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech) => (
                    <Badge key={tech} variant="secondary" className="text-xs">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Key Features */}
              <div>
                <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-3">
                  Key Features
                </h4>
                <ul className="space-y-2">
                  {project.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-zinc-700 dark:text-zinc-300">
                      <div className="w-1.5 h-1.5 bg-sidebar-foreground rounded-full mt-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Engineering Highlights */}
              <div>
                <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-3">
                  Engineering Highlights
                </h4>
                <ul className="space-y-2">
                  {project.highlights.map((highlight, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-zinc-700 dark:text-zinc-300">{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Detailed Accomplishments for DataExpert Bootcamp */}
              {project.accomplishments && (
                <div>
                  <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 mb-3">
                    Key Accomplishments by Module
                  </h4>
                  <div className="space-y-4">
                    {Object.entries(project.accomplishments).map(([module, accomplishments]) => (
                      <div key={module} className="border-l-2 border-sidebar-border pl-4">
                        <h5 className="font-medium text-zinc-800 dark:text-zinc-200 mb-2">{module}</h5>
                        <ul className="space-y-2">
                          {accomplishments.map((accomplishment, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-zinc-700 dark:text-zinc-300">
                              <div className="w-1 h-1 bg-sidebar-foreground rounded-full mt-2 flex-shrink-0" />
                              <span>{accomplishment}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </PageLayout>
  );
}
