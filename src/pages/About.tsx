import { PageLayout } from "@/components/PageLayout";
import { SITE_CONFIG } from "@/lib/constants";
import { Badge } from "@/components/ui/badge";
import { MapPin, Mail, Linkedin, Github, Calendar, Users, Award, BarChart3 } from "lucide-react";
import { Link } from "react-router-dom";
import { CVDownloadButton } from "@/components/CVDownloadButton";

export default function About() {
  const technicalSkills = [
    { name: "Python", context: "Data pipelines, AI/ML, automation" },
    { name: "SQL", context: "Complex queries, database optimization" },
    { name: "Golang", context: "CLI tools, performance-critical apps" },
    { name: "Airflow/Dagster", context: "ETL/ELT workflows" },
    { name: "Kafka", context: "Real-time data streaming" },
    { name: "AWS", context: "Cloud data solutions" },
    { name: "dbt", context: "Data modeling & warehousing" },
    { name: "Tableau", context: "Data visualization and dashboarding" },
  ];

  const languages = [
    { language: "English", level: "Fluent (C2)" },
    { language: "Spanish", level: "Fluent (C2)" },
    { language: "Ukrainian", level: "Native" },
    { language: "Russian", level: "Native" },
    { language: "Basque", level: "Conversational (B1)" }
  ];

  const achievements = [
    "Led 6-member team achieving 30% quality improvement and 20% productivity gain",
    "Reduced in-game text discrepancies by 20% through systematic data validation",
    "Built automation tools eliminating 100% of manual errors",
    "Designed Kafka streaming architecture handling 30k events/sec",
    "Successfully shipped multiple AAA titles meeting Microsoft and Sony standards"
  ];

  return (
    <PageLayout currentPage="about">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-4xl font-bold mb-2">About Me</h1>
            <div className="text-zinc-500 dark:text-zinc-500 text-sm flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              May 17, 2025 · 5 min · {SITE_CONFIG.name}
            </div>
          </div>
          <CVDownloadButton
            variant="outline"
            className="bg-zinc-100 hover:bg-zinc-200 dark:bg-zinc-800 dark:hover:bg-zinc-700 text-zinc-900 dark:text-zinc-100 border-zinc-200 dark:border-zinc-700"
          />
        </div>
      </div>

      {/* Hero Introduction */}
      <section className="mb-12 bg-gradient-to-r from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800 rounded-lg p-6">
        <div className="flex items-start gap-4">
          <div className="flex-1">
            <h2 className="text-2xl font-semibold mb-2 text-zinc-800 dark:text-zinc-200">
              Engineer & Linguist | Data + Localization Expert
            </h2>
            <p className="text-zinc-700 dark:text-zinc-300 leading-relaxed mb-4">
              An engineer and linguist with expertise in localization and data - a rare combination that brings both
              technical depth and global perspective to complex problems. Proven ability to lead teams, design robust
              data solutions, and leverage AI for innovative applications.
            </p>
            <div className="flex items-center gap-4 text-sm text-zinc-600 dark:text-zinc-400">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                Cuenca, Spain
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                EU National - Authorized to work throughout EU
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="mb-8">
        <p className="text-zinc-700 dark:text-zinc-300 leading-relaxed">
          If you're here looking for my technical work, check out my{" "}
          <Link
            to="/projects"
            className="text-zinc-600 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-300 underline font-medium"
          >
            coding projects portfolio
          </Link>
          {" "}and{" "}
          <Link
            to="/vizzes"
            className="text-zinc-600 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-300 underline font-medium"
          >
            data viz projects
          </Link>
          {", or jump right into "}
          <Link
            to="/chat"
            className="text-zinc-600 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-300 underline font-medium"
          >
            chatting with my AI assistant
          </Link>
          {" for a more interactive experience."}
        </p>
      </section>

      {/* Technical Skills (multiple boxes, no level) */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
          <Award className="h-6 w-6" />
          Technical Expertise
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          {technicalSkills.map((skill) => (
            <div
              key={skill.name}
              className="rounded-lg bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 p-5 flex flex-col"
            >
              <span className="font-semibold text-zinc-900 dark:text-zinc-50 mb-1 text-lg">{skill.name}</span>
              <span className="text-zinc-600 dark:text-zinc-400">{skill.context}</span>
            </div>
          ))}
        </div>
      </section>

      {/* Key Achievements */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200">
          Key Achievements
        </h2>
        <div className="space-y-3">
          {achievements.map((achievement, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="w-2 h-2 bg-zinc-600 rounded-full mt-2 flex-shrink-0" />
              <p className="text-zinc-700 dark:text-zinc-300">{achievement}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Experience Overview */}
      <section id="experience" className="mb-12">
        <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200">
          Professional Experience
        </h2>

        {/* Two-column grid */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Electronic Arts */}
          <div className="border-l-4 border-zinc-600 pl-6 bg-white dark:bg-zinc-900 rounded-r-lg p-4 border border-l-4 border-zinc-200 dark:border-zinc-800">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
              <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">
                Electronic Arts
              </h3>
              <div className="text-sm text-zinc-500 dark:text-zinc-500">
                June 2021 – Oct 2024, 2025
              </div>
            </div>
            <h4 className="text-lg text-zinc-600 dark:text-zinc-400 mb-3 font-medium">
              Localization Quality Analyst Lead
            </h4>
            <p className="text-zinc-700 dark:text-zinc-300 mb-4">
              Led a 6-person Localization QA team in the gaming industry, building scalable quality assurance
              systems and automation tools. Applied engineering principles to streamline localization workflows,
              resolve language-specific bugs, and ensure high-quality game experiences across markets.
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <span className="text-zinc-600 dark:text-zinc-400 font-medium">•</span>
                <span className="text-zinc-700 dark:text-zinc-300">
                  Successfully shipped multiple AAA titles including EA SPORTS FC, F1, and Sims 4 DLCs
                </span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-zinc-600 dark:text-zinc-400 font-medium">•</span>
                <span className="text-zinc-700 dark:text-zinc-300">
                  Developed Python automation tools for data synchronization (Gridly + CRM integration)
                </span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-zinc-600 dark:text-zinc-400 font-medium">•</span>
                <span className="text-zinc-700 dark:text-zinc-300">
                  Created metrics tracking systems across 5+ departments
                </span>
              </div>
            </div>
          </div>

          {/* Appen */}
          <div className="border-l-4 border-zinc-600 pl-6 bg-white dark:bg-zinc-900 rounded-r-lg p-4 border border-l-4 border-zinc-200 dark:border-zinc-800">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
              <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">
                Appen
              </h3>
              <div className="text-sm text-zinc-500 dark:text-zinc-500">
                May 2019 – May 2023
              </div>
            </div>
            <h4 className="text-lg text-zinc-600 dark:text-zinc-400 mb-3 font-medium">
              Language Analyst / AI Data Annotator
            </h4>
            <p className="text-zinc-700 dark:text-zinc-300 mb-4">
              Contributed to ML model improvement through high-quality data annotation and analysis for major
              tech companies including Google, Meta, and Twitter.
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <span className="text-zinc-600 dark:text-zinc-400 font-medium">•</span>
                <span className="text-zinc-700 dark:text-zinc-300">
                  Enhanced sentiment analysis accuracy for major search engines and social media platforms
                </span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-zinc-600 dark:text-zinc-400 font-medium">•</span>
                <span className="text-zinc-700 dark:text-zinc-300">
                  Improved user engagement metrics by 25% through systematic content analysis
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Compact grid for Language & Education */}
      <section className="mb-12">
        <div className="grid md:grid-cols-2 gap-8">
          {/* Language Skills */}
          <div>
            <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200">
              Language Proficiencies
            </h2>
            <ul className="list-disc pl-6 space-y-2">
              {languages.map((lang) => (
                <li key={lang.language} className="text-zinc-700 dark:text-zinc-300">
                  <span className="font-medium text-zinc-900 dark:text-zinc-50">{lang.language}</span>
                  {" – "}
                  <span className="text-zinc-600 dark:text-zinc-400">{lang.level}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200">
              Education & Certifications
            </h2>
            <div className="space-y-4">
              <div className="border-l-2 border-zinc-300 dark:border-zinc-700 pl-4">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">DataExpert.io Data Engineering Bootcamp</h3>
                <p className="text-zinc-600 dark:text-zinc-400 text-sm">Dec 2024 • Intensive hands-on training in production data engineering</p>
              </div>
              <div className="border-l-2 border-zinc-300 dark:border-zinc-700 pl-4">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Boot.dev Computer Science Program</h3>
                <p className="text-zinc-600 dark:text-zinc-400 text-sm">Jan 2025 • Comprehensive CS fundamentals with Back-End focus</p>
              </div>
              <div className="border-l-2 border-zinc-300 dark:border-zinc-700 pl-4">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Wharton Business Analytics Specialization</h3>
                <p className="text-zinc-600 dark:text-zinc-400 text-sm">Dec 2021 • Statistical modeling, data analysis, business intelligence</p>
              </div>
              <div className="border-l-2 border-zinc-300 dark:border-zinc-700 pl-4">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Bachelor of Arts</h3>
                <p className="text-zinc-600 dark:text-zinc-400 text-sm">May 2021 • UNED, Madrid, Spain</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6 text-zinc-800 dark:text-zinc-200">
          Get In Touch
        </h2>
        <div className="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6">
          <p className="text-zinc-700 dark:text-zinc-300 mb-6">
            I'm currently seeking opportunities that blend technical engineering skills with data expertise,
            where linguistic knowledge and leadership experience add unique value. Open to remote, hybrid, or
            relocation for the right opportunity.
          </p>

          {/* CV Download Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 p-4 bg-zinc-50 dark:bg-zinc-900 rounded-lg">
            <div>
              <h3 className="font-medium text-zinc-900 dark:text-zinc-100 mb-1">Resume / CV</h3>
              <p className="text-sm text-zinc-600 dark:text-zinc-400">
                Download my complete resume for detailed work history and technical experience
              </p>
            </div>
            <CVDownloadButton />
          </div>

          <div className="flex justify-center items-center gap-6">
            <a
              href={`mailto:${SITE_CONFIG.email}`}
              aria-label="Email"
              className="inline-flex items-center justify-center size-10 border border-zinc-300 bg-zinc-200 text-zinc-800 rounded-full hover:bg-zinc-300 transition-colors dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:hover:bg-zinc-600"
            >
              <Mail className="h-4 w-4" />
            </a>
            <a
              href={SITE_CONFIG.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="LinkedIn"
              className="inline-flex items-center justify-center size-10 border border-zinc-300 bg-zinc-200 text-zinc-800 rounded-full hover:bg-zinc-300 transition-colors dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:hover:bg-zinc-600"
            >
              <Linkedin className="h-4 w-4" />
            </a>
            <a
              href={SITE_CONFIG.github}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
              className="inline-flex items-center justify-center size-10 border border-zinc-300 bg-zinc-200 text-zinc-800 rounded-full hover:bg-zinc-300 transition-colors dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:hover:bg-zinc-600"
            >
              <Github className="h-4 w-4" />
            </a>
            <a
              href="https://public.tableau.com/app/profile/maksym.ionutsa/vizzes"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Tableau Public Profile"
              className="inline-flex items-center justify-center size-10 border border-zinc-300 bg-zinc-200 text-zinc-800 rounded-full hover:bg-zinc-300 transition-colors dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:hover:bg-zinc-600"
            >
              <BarChart3 className="h-4 w-4" />
            </a>
          </div>
        </div>
      </section>
    </PageLayout>
  );
}
