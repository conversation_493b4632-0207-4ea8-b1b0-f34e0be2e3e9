import { GoogleGenerativeAI } from "@google/generative-ai";

export class EmbeddingService {
    private model: any = null;
    private isInitialized = false;

    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        const apiKey = process.env.VITE_GEMINI_API_KEY || process.env.GEMINI_API_KEY;
        if (!apiKey) {
            throw new Error("Gemini API key not found in environment variables");
        }
        try {
            const genAI = new GoogleGenerativeAI(apiKey);
            this.model = genAI.getGenerativeModel({ model: "text-embedding-004" });
            this.isInitialized = true;
            console.log("Gemini embedding model initialized (text-embedding-004)");
        } catch (error) {
            console.error("Failed to initialize Gemini embedding model:", error);
            throw new Error("Failed to initialize Gemini embedding model");
        }
    }

    async generateEmbedding(text: string): Promise<number[]> {
        if (!this.isInitialized || !this.model) {
            throw new Error("Embedding service not initialized");
        }
        // Gemini's input token limit is 2048 tokens, so truncate if needed
        const maxLength = 8000; // Truncate to 8000 chars for safety
        const truncatedText = text.length > maxLength ? text.substring(0, maxLength) : text;
        try {
            const result = await this.model.embedContent(truncatedText);
            if (!result || !result.embedding || !result.embedding.values) {
                throw new Error("No embedding returned from Gemini API");
            }
            return result.embedding.values;
        } catch (error) {
            console.error("Error generating Gemini embedding:", error);
            throw new Error("Failed to generate Gemini embedding");
        }
    }

    dispose(): void {
        this.model = null;
        this.isInitialized = false;
    }
}