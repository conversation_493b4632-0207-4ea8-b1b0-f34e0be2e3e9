import { supabase } from "@/integrations/supabase/client";

// Simple LRU implementation for in-memory cache
const LRU_CACHE_MAX = 128;
const lruMap: Map<string, number[]> = new Map();

function setLRU(key: string, value: number[]) {
  if (lruMap.has(key)) {
    lruMap.delete(key);
  } else if (lruMap.size >= LRU_CACHE_MAX) {
    // Remove oldest entry
    const oldestKey = lruMap.keys().next().value;
    lruMap.delete(oldestKey);
  }
  lruMap.set(key, value);
}

function getLRU(key: string): number[] | undefined {
  if (!lruMap.has(key)) return undefined;
  const val = lruMap.get(key)!;
  // Refresh key
  lruMap.delete(key);
  lruMap.set(key, val);
  return val;
}

export class QueryEmbeddingCache {

  // Generate a simple hash for query caching
  private generateQueryHash(query: string): string {
    return btoa(query.toLowerCase().trim()).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
  }

  // Check if query embedding is cached (with LRU logic)
  async getCachedEmbedding(query: string): Promise<number[] | null> {
    const queryHash = this.generateQueryHash(query);

    // Fast local in-memory LRU check
    const memoryCached = getLRU(queryHash);
    if (memoryCached) {
      // Local hit
      return memoryCached;
    }

    try {
      const { data, error } = await supabase.rpc('get_or_cache_query_embedding', {
        query_text: query,
        query_hash: queryHash
      });

      if (error) {
        console.warn("Error retrieving cached embedding:", error);
        return null;
      }

      if (data) {
        // Convert pgvector format back to array
        let parsed: number[];
        if (typeof data === 'string') {
          parsed = JSON.parse(data);
        } else {
          parsed = data;
        }
        setLRU(queryHash, parsed);
        return parsed;
      }
    } catch (error) {
      console.warn("Error retrieving cached embedding:", error);
    }
    return null;
  }

  // Cache a query embedding
  async cacheEmbedding(query: string, embedding: number[]): Promise<void> {
    const queryHash = this.generateQueryHash(query);
    const embeddingVector = `[${embedding.join(',')}]`;

    // Add to LRU cache
    setLRU(queryHash, embedding);

    try {
      const { error } = await supabase.rpc('get_or_cache_query_embedding', {
        query_text: query,
        query_hash: queryHash,
        embedding_vector: embeddingVector
      });

      if (error) {
        console.warn("Error caching embedding:", error);
      } else {
        console.log("💾 Query embedding cached successfully");
      }
    } catch (error) {
      console.warn("Error caching embedding:", error);
    }
  }

  // Get or generate embedding with caching (LRU-aware)
  async getOrGenerateEmbedding(
    query: string,
    generateFn: (text: string) => Promise<number[]>
  ): Promise<number[]> {
    // Try to get from cache first
    const cachedEmbedding = await this.getCachedEmbedding(query);
    if (cachedEmbedding) {
      return cachedEmbedding;
    }
    // Generate new embedding
    console.log("Generating new embedding for query");
    const embedding = await generateFn(query);

    // Cache the result (in-memory and persistent)
    await this.cacheEmbedding(query, embedding);

    return embedding;
  }

  // Clean up old cache entries (for maintenance)
  async cleanupCache(daysOld: number = 30, minAccessCount: number = 2): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_query_cache', {
        days_old: daysOld,
        min_access_count: minAccessCount
      });

      if (error) {
        console.error("Error cleaning up cache:", error);
        return 0;
      }

      console.log(`Cleaned up ${data || 0} old cache entries`);
      return data || 0;
    } catch (error) {
      console.error("Error cleaning up cache:", error);
      return 0;
    }
  }
}
