import { supabase } from "@/integrations/supabase/client";

// Simple LRU implementation for in-memory cache
const LRU_CACHE_MAX = 128;
const lruMap: Map<string, number[]> = new Map();

function setLRU(key: string, value: number[]) {
  if (lruMap.has(key)) {
    lruMap.delete(key);
  } else if (lruMap.size >= LRU_CACHE_MAX) {
    // Remove oldest entry
    const oldestKey = lruMap.keys().next().value;
    lruMap.delete(oldestKey);
  }
  lruMap.set(key, value);
}

function getLRU(key: string): number[] | undefined {
  if (!lruMap.has(key)) return undefined;
  const val = lruMap.get(key)!;
  // Refresh key
  lruMap.delete(key);
  lruMap.set(key, val);
  return val;
}

export class QueryEmbeddingCache {

  // Generate a simple hash for query caching
  private generateQueryHash(query: string): string {
    const normalized = query.toLowerCase().trim();
    console.log(`🔑 [Cache] Generating hash for normalized query: "${normalized.substring(0, 100)}..."`);

    try {
      const hash = btoa(normalized).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
      console.log(`🔑 [Cache] Generated hash: ${hash}`);
      return hash;
    } catch (error) {
      console.error("❌ [Cache] Error generating hash:", error);
      // Fallback to simple hash if btoa fails
      const fallbackHash = normalized.replace(/[^a-zA-Z0-9]/g, '').substring(0, 50);
      console.log(`🔑 [Cache] Using fallback hash: ${fallbackHash}`);
      return fallbackHash;
    }
  }

  // Check if query embedding is cached (with LRU logic)
  async getCachedEmbedding(query: string): Promise<number[] | null> {
    const queryHash = this.generateQueryHash(query);
    console.log(`🔍 [Cache] Checking cache for query: "${query.substring(0, 100)}..." | Hash: ${queryHash}`);

    // Fast local in-memory LRU check
    const memoryCached = getLRU(queryHash);
    if (memoryCached) {
      console.log(`✅ [Cache] LRU cache HIT for hash: ${queryHash} | Dimensions: ${memoryCached.length}`);
      return memoryCached;
    }
    console.log(`❌ [Cache] LRU cache MISS for hash: ${queryHash}`);

    try {
      console.log(`🔍 [Cache] Checking database cache for hash: ${queryHash}`);
      const { data, error } = await supabase.rpc('get_or_cache_query_embedding', {
        query_text: query,
        query_hash: queryHash
      });

      if (error) {
        console.error("❌ [Cache] Database cache error:", error);
        console.error("❌ [Cache] Error details:", JSON.stringify(error, null, 2));
        return null;
      }

      if (data) {
        console.log(`✅ [Cache] Database cache HIT for hash: ${queryHash} | Data type: ${typeof data}`);
        // Convert pgvector format back to array
        let parsed: number[];
        if (typeof data === 'string') {
          parsed = JSON.parse(data);
        } else {
          parsed = data;
        }
        console.log(`✅ [Cache] Parsed embedding dimensions: ${parsed.length}`);
        setLRU(queryHash, parsed);
        return parsed;
      } else {
        console.log(`❌ [Cache] Database cache MISS for hash: ${queryHash}`);
      }
    } catch (error) {
      console.error("❌ [Cache] Exception retrieving cached embedding:", error);
      console.error("❌ [Cache] Exception stack:", error instanceof Error ? error.stack : 'No stack trace');
    }
    return null;
  }

  // Cache a query embedding
  async cacheEmbedding(query: string, embedding: number[]): Promise<void> {
    const queryHash = this.generateQueryHash(query);
    // PostgreSQL vector format: '[1,2,3,...]'
    const embeddingVector = `[${embedding.join(',')}]`;

    console.log(`💾 [Cache] Caching embedding for query: "${query.substring(0, 100)}..." | Hash: ${queryHash}`);
    console.log(`💾 [Cache] Embedding dimensions: ${embedding.length}`);
    console.log(`💾 [Cache] Vector format sample: ${embeddingVector.substring(0, 100)}...`);

    // Add to LRU cache
    setLRU(queryHash, embedding);
    console.log(`💾 [Cache] Added to LRU cache. Current LRU size: ${lruMap.size}`);

    try {
      const { error } = await supabase.rpc('get_or_cache_query_embedding', {
        query_text: query,
        query_hash: queryHash,
        embedding_vector: embeddingVector  // ✅ Send as PostgreSQL vector string format
      });

      if (error) {
        console.error("❌ [Cache] Error caching embedding:", error);
        console.error("❌ [Cache] Caching error details:", JSON.stringify(error, null, 2));
      } else {
        console.log(`✅ [Cache] Query embedding cached successfully in database for hash: ${queryHash}`);
      }
    } catch (error) {
      console.error("❌ [Cache] Exception caching embedding:", error);
      console.error("❌ [Cache] Exception stack:", error instanceof Error ? error.stack : 'No stack trace');
    }
  }

  // Get or generate embedding with caching (LRU-aware)
  async getOrGenerateEmbedding(
    query: string,
    generateFn: (text: string) => Promise<number[]>
  ): Promise<number[]> {
    // Try to get from cache first
    const cachedEmbedding = await this.getCachedEmbedding(query);
    if (cachedEmbedding) {
      return cachedEmbedding;
    }
    // Generate new embedding
    console.log("Generating new embedding for query");
    const embedding = await generateFn(query);

    // Cache the result (in-memory and persistent)
    await this.cacheEmbedding(query, embedding);

    return embedding;
  }

  // Clean up old cache entries (for maintenance)
  async cleanupCache(daysOld: number = 30, minAccessCount: number = 2): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('cleanup_query_cache', {
        days_old: daysOld,
        min_access_count: minAccessCount
      });

      if (error) {
        console.error("Error cleaning up cache:", error);
        return 0;
      }

      console.log(`Cleaned up ${data || 0} old cache entries`);
      return data || 0;
    } catch (error) {
      console.error("Error cleaning up cache:", error);
      return 0;
    }
  }
}
