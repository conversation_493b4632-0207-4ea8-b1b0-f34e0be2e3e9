import { VectorStore, SearchResult } from "./vectorStore";
import { DocumentProcessor } from "./documentProcessor";
import { QueryEmbeddingCache } from "./queryEmbeddingCache";
import { EmbeddingService } from "./embeddingService";
import { RAGDocument, RetrievedDocument } from "./types";

export class RAGRetriever {
  private vectorStore: VectorStore;
  private documentProcessor: DocumentProcessor;
  private embeddingCache: QueryEmbeddingCache;
  private embeddingService: EmbeddingService;
  private isInitialized = false;

  constructor() {
    this.vectorStore = new VectorStore();
    this.documentProcessor = new DocumentProcessor();
    this.embeddingCache = new QueryEmbeddingCache();
    this.embeddingService = new EmbeddingService();
  }

  async initialize() {
    console.log("[RAGRetriever] Initializing RAG components...");
    await this.vectorStore.initialize();
    await this.embeddingService.initialize();

    const hasEmbeddings = await this.vectorStore.hasLoadedPrecomputedEmbeddings();
    const docCount = await this.vectorStore.getLoadedDocumentCount();

    console.log(`[RAGRetriever] Init status: hasEmbeddings=${hasEmbeddings}, docCount=${docCount}`);

    if (hasEmbeddings && docCount > 0) {
      console.log(`[RAGRetriever] Using ${docCount} precomputed embeddings with pgvector optimization`);

      // Debug: Check what's actually in the database
      await this.vectorStore.debugDocumentContent(3);

      this.isInitialized = true;
      console.log("[RAGRetriever] RAG system fully initialized with optimized vector search");
      return;
    }

    console.log("[RAGRetriever] No embeddings found in database. You need to run the precompute script first.");
    console.log("[RAGRetriever] Run: npm run precompute-embeddings");

    this.isInitialized = true;
    console.log("[RAGRetriever] RAG system initialized but no embeddings available");
  }

  async indexDocuments() {
    console.log("[RAGRetriever] Loading raw documents from filesystem...");
    const rawDocuments = await this.documentProcessor.loadDocuments();
    console.log(`[RAGRetriever] Found ${rawDocuments.length} original document sources`);

    if (rawDocuments.length === 0) {
      console.warn("[RAGRetriever] No documents found to index.");
      return;
    }

    let allProcessableChunks: RAGDocument[] = [];
    for (const doc of rawDocuments) {
      const docChunks = this.documentProcessor.chunkDocument(doc);
      if (docChunks.length > 0) {
        allProcessableChunks.push(...docChunks);
        if (docChunks.length > 1) {
          console.log(
            `[RAGRetriever] Split ${doc.metadata.filename} (Project: ${doc.metadata.project || "N/A"}, Type: ${doc.metadata.type}) into ${docChunks.length} chunks`
          );
        }
      }
    }

    console.log(`[RAGRetriever] Total document chunks generated: ${allProcessableChunks.length}`);

    if (allProcessableChunks.length > 0) {
      // Note: This method expects embeddings to be pre-generated
      console.log("[RAGRetriever] Documents processed. Use precompute script to generate embeddings.");
    }
  }

  async retrieve(query: string, k = 3): Promise<RetrievedDocument[]> {
    console.log(`[RAGRetriever] Starting retrieval for query: "${query}" with k=${k}`);

    if (!this.isInitialized) {
      console.warn("[RAGRetriever] Not initialized, attempting to retrieve might fail or be empty.");
      return [];
    }

    try {
      console.log(`[RAGRetriever] Processing query with caching: "${query}"`);

      const queryEmbedding = await this.embeddingCache.getOrGenerateEmbedding(
        query,
        (text) => this.embeddingService.generateEmbedding(text)
      );

      console.log(`[RAGRetriever] Generated query embedding with ${queryEmbedding.length} dimensions`);

      const searchResults = await this.vectorStore.search(queryEmbedding, k);
      console.log(`[RAGRetriever] Vector search returned ${searchResults.length} results`);

      const retrievedDocs = searchResults.map(result => ({
        content: result.document.content,
        metadata: result.document.metadata,
        similarity: result.score
      }));

      // Log details about retrieved documents
      retrievedDocs.forEach((doc, idx) => {
        console.log(`[RAGRetriever] Doc ${idx + 1}: similarity=${doc.similarity.toFixed(3)}, content_length=${doc.content.length}, metadata=`, doc.metadata);
      });

      return retrievedDocs;
    } catch (error) {
      console.error("[RAGRetriever] Error in retrieve:", error);
      return [];
    }
  }

  formatContext(retrievedDocs: RetrievedDocument[]): string {
    return retrievedDocs
      .map((doc, idx) => {
        let sourceInfo = `Type: ${doc.metadata.type}`;
        if (doc.metadata.project) {
          sourceInfo += `, Project: ${doc.metadata.project}`;
        }
        const fileIdentifier = doc.metadata.relative_path || doc.metadata.filename;
        if (fileIdentifier) {
          sourceInfo += `, SourceFile: ${fileIdentifier}`;
        }
        const similarityScore = (doc.similarity * 100).toFixed(1);
        return `[Context Snippet ${idx + 1} | ${sourceInfo} | Similarity: ${similarityScore}%]:\n${doc.content}`;
      })
      .join("\n\n---\n\n");
  }

  async cleanupEmbeddingCache(): Promise<number> {
    return await this.embeddingCache.cleanupCache();
  }
}