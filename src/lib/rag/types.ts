
export interface RAGDocument {
  id: string;
  content: string;
  metadata: {
    type: "personal_info" | "project_readme" | "code";
    filename: string;
    project?: string;
    relative_path?: string;
    [key: string]: any;
  };
}

export interface RetrievedDocument {
  content: string;
  metadata: any;
  similarity: number;
}

export interface StoredEmbeddings {
  documents: RAGDocument[];
  embeddings: number[][];
}
