import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, Moon, Sun } from "lucide-react";
import { SITE_CONFIG } from "@/lib/constants";
import { useTheme } from "@/contexts/ThemeContext";
import { CVDownloadButton } from "@/components/CVDownloadButton";

interface NavigationProps {
  currentPage?: string;
}

export const Navigation = ({ currentPage }: NavigationProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();

  const isActive = (path: string) => {
    if (currentPage) {
      return currentPage === path.replace('/', '') || (path === '/' && (currentPage === 'home' || currentPage === 'about'));
    }
    return location.pathname === path;
  };

  return (
    <nav className="bg-white dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-800 sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link
            to="/"
            className="text-xl font-bold text-zinc-900 dark:text-zinc-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {SITE_CONFIG.name}
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`transition-colors ${isActive('/')
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-zinc-700 dark:text-zinc-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
            >
              About
            </Link>
            <Link
              to="/chat"
              className={`transition-colors ${isActive('/chat')
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-zinc-700 dark:text-zinc-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
            >
              Chat
            </Link>
            <Link
              to="/projects"
              className={`transition-colors ${isActive('/projects')
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-zinc-700 dark:text-zinc-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
            >
              Projects
            </Link>
            <Link
              to="/vizzes"
              className={`transition-colors ${isActive('/vizzes')
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-zinc-700 dark:text-zinc-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
            >
              Vizzes
            </Link>
            <Link
              to="/blog"
              className={`transition-colors ${isActive('/blog')
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-zinc-700 dark:text-zinc-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
            >
              Blog
            </Link>

            {/* CV Download Button */}
            <CVDownloadButton variant="ghost" size="sm">
              CV
            </CVDownloadButton>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-2">
            <CVDownloadButton variant="ghost" size="sm">
              CV
            </CVDownloadButton>
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-zinc-200 dark:border-zinc-800">
            <div className="flex flex-col space-y-3">
              <Link
                to="/"
                onClick={() => setIsMenuOpen(false)}
                className={`px-3 py-2 rounded-lg transition-colors ${isActive('/')
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                  }`}
              >
                About
              </Link>
              <Link
                to="/chat"
                onClick={() => setIsMenuOpen(false)}
                className={`px-3 py-2 rounded-lg transition-colors ${isActive('/chat')
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                  }`}
              >
                Chat
              </Link>
              <Link
                to="/projects"
                onClick={() => setIsMenuOpen(false)}
                className={`px-3 py-2 rounded-lg transition-colors ${isActive('/projects')
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                  }`}
              >
                Projects
              </Link>
              <Link
                to="/vizzes"
                onClick={() => setIsMenuOpen(false)}
                className={`px-3 py-2 rounded-lg transition-colors ${isActive('/vizzes')
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                  }`}
              >
                Vizzes
              </Link>
              <Link
                to="/blog"
                onClick={() => setIsMenuOpen(false)}
                className={`px-3 py-2 rounded-lg transition-colors ${isActive('/blog')
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                  }`}
              >
                Blog
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
