
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface CVDownloadButtonProps {
  variant?: "default" | "outline" | "ghost" | "link" | "destructive" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export const CVDownloadButton = ({ 
  variant = "default", 
  size = "default", 
  className = "",
  children 
}: CVDownloadButtonProps) => {
  const { toast } = useToast();

  const handleDownload = async () => {
    try {
      // Get the CV file from Supabase storage
      const { data, error } = await supabase.storage
        .from('documents')
        .download('maksym-ionutsa-cv.pdf');

      if (error) {
        console.error('Error downloading CV:', error);
        toast({
          title: "Download Error",
          description: "CV file not found. Please contact me directly for my resume.",
          variant: "destructive",
        });
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'Maksym_Ionutsa_CV.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download Started",
        description: "Your CV download has begun!",
      });
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Download Error",
        description: "Something went wrong. Please try again or contact me directly.",
        variant: "destructive",
      });
    }
  };

  return (
    <Button 
      onClick={handleDownload}
      variant={variant}
      size={size}
      className={className}
    >
      <Download className="h-4 w-4 mr-2" />
      {children || "Download CV"}
    </Button>
  );
};
