import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { supabase } from '@/integrations/supabase/client';
import { DocumentProcessor } from '@/lib/rag/documentProcessor';
import { RAGDocument } from '@/lib/rag/types';

export const EmbeddingPrecompute = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, message]);
  };

  const precomputeEmbeddings = async () => {
    setIsRunning(true);
    setProgress(0);
    setLogs([]);
    setError(null);

    try {
      addLog("Starting embedding precomputation with Hugging Face transformers...");
      setStatus("Loading embedding pipeline...");

      // Load the embedding pipeline
      const { pipeline } = await import('@huggingface/transformers');
      const embedder = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
      addLog("Text embedding pipeline loaded successfully");
      setProgress(20);

      setStatus("Loading documents...");

      // Load static documents (since we can't read filesystem in browser)
      const staticDocuments: RAGDocument[] = [
        {
          id: "personal_info_identity",
          content: JSON.stringify({
            name: "Maksym Ionutsa",
            title: "Engineer and Linguist",
            location: "Ukraine",
            languages: ["Ukrainian", "English", "Russian", "Polish"],
            interests: ["Data Engineering", "Analytics", "Localization", "Quality Assurance"]
          }),
          metadata: {
            type: "personal_info",
            filename: "identity-and-contact.json"
          }
        },
        {
          id: "skills_technical",
          content: JSON.stringify({
            programming: ["Python", "SQL", "JavaScript", "TypeScript"],
            databases: ["PostgreSQL", "MySQL", "MongoDB"],
            tools: ["Git", "Docker", "Jenkins", "Jira"],
            analytics: ["Data Analysis", "ETL", "Reporting", "Dashboards"],
            qa: ["Test Planning", "Bug Tracking", "Quality Metrics", "Process Improvement"]
          }),
          metadata: {
            type: "personal_info",
            filename: "technical-skills.json"
          }
        },
        {
          id: "experience_work",
          content: JSON.stringify({
            current_focus: "Transitioning from localization QA in GameDev to data engineering/analytics roles",
            background: "Systematic problem-solving, attention to detail, data integrity focus",
            transferable_skills: ["Quality assurance methodologies", "Data validation", "Process optimization", "Cross-functional collaboration"]
          }),
          metadata: {
            type: "personal_info",
            filename: "work-experience.json"
          }
        }
      ];

      const processor = new DocumentProcessor();
      addLog(`Using ${staticDocuments.length} static document sources.`);

      let allChunks: RAGDocument[] = [];
      addLog("Chunking documents...");
      setProgress(30);

      for (const doc of staticDocuments) {
        const docChunks = processor.chunkDocument(doc);
        if (docChunks.length > 0) {
          allChunks.push(...docChunks);
          if (docChunks.length > 1) {
            addLog(`Split ${doc.metadata.filename} into ${docChunks.length} chunks`);
          }
        }
      }

      addLog(`Total chunks generated: ${allChunks.length}`);
      setProgress(40);

      if (allChunks.length === 0) {
        throw new Error("No chunks generated from documents");
      }

      setStatus("Clearing existing embeddings...");
      addLog("Clearing existing embeddings from database...");
      const { error: clearError } = await supabase
        .from('document_embeddings')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000');

      if (clearError) {
        throw new Error(`Error clearing existing embeddings: ${clearError.message}`);
      }

      setStatus("Generating embeddings...");
      addLog("Generating embeddings with Hugging Face transformers...");
      setProgress(50);

      const batchSize = 3;
      
      for (let i = 0; i < allChunks.length; i += batchSize) {
        const batch = allChunks.slice(i, i + batchSize);
        const batchNum = Math.floor(i/batchSize) + 1;
        const totalBatches = Math.ceil(allChunks.length/batchSize);
        
        addLog(`Processing batch ${batchNum}/${totalBatches}...`);
        setStatus(`Processing batch ${batchNum}/${totalBatches}...`);
        
        const embeddingPromises = batch.map(async (chunk) => {
          const output = await embedder(chunk.content, { pooling: 'mean', normalize: true });
          const embedding = Array.from(output.data);
          return {
            document_id: chunk.id,
            content: chunk.content,
            metadata: chunk.metadata,
            embedding: `[${embedding.join(',')}]` // Store as pgvector format
          };
        });

        const embeddingsData = await Promise.all(embeddingPromises);
        
        const { error } = await supabase
          .from('document_embeddings')
          .insert(embeddingsData);

        if (error) {
          throw new Error(`Error inserting batch ${batchNum}: ${error.message}`);
        }

        addLog(`Processed batch ${batchNum}/${totalBatches} (${batch.length} chunks)`);
        setProgress(50 + (batchNum / totalBatches) * 40);
      }

      setStatus("Verifying embeddings...");
      addLog("Verifying stored embeddings...");
      const { data: storedEmbeddings, error: countError } = await supabase
        .from('document_embeddings')
        .select('id')
        .limit(1000);

      if (countError) {
        throw new Error(`Error verifying embeddings: ${countError.message}`);
      }

      setProgress(100);
      addLog(`Successfully stored ${storedEmbeddings?.length || 0} embeddings!`);
      addLog("Embedding precomputation complete - production ready!");
      setStatus("Complete - Production Ready!");

    } catch (error: any) {
      console.error("Embedding precomputation failed:", error);
      setError(error.message);
      addLog(`Error: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Production Embedding Precomputation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm">{status || "Ready to start with production embeddings"}</span>
            <span className="text-sm">{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        <Button 
          onClick={precomputeEmbeddings} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? "Processing..." : "Precompute Production Embeddings"}
        </Button>

        {logs.length > 0 && (
          <div className="border rounded p-3 h-64 overflow-y-auto bg-gray-50 dark:bg-gray-900">
            <div className="text-xs font-mono space-y-1">
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
