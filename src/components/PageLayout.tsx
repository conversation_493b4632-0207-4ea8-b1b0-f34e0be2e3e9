import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";
import { Breadcrumb } from "@/components/Breadcrumb";

interface PageLayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  showBreadcrumb?: boolean;
}

export const PageLayout = ({
  children,
  currentPage,
  showBreadcrumb = true
}: PageLayoutProps) => {
  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 transition-colors">
      <Navigation currentPage={currentPage} />

      <main className="max-w-5xl mx-auto px-6 py-12">
        {showBreadcrumb && <Breadcrumb />}
        {children}
      </main>

      <Footer />
    </div>
  );
};
