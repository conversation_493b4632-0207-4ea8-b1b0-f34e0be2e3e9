import { GoogleGenerativeAI } from "@google/generative-ai";
import { supabase } from "@/integrations/supabase/client";

// Types
type SupabaseDocumentRow = {
  content: string;
  metadata: any;
  document_id: string;
  embedding: number[]; // Add this for vector search
};

type DocumentResult = {
  content: string;
  metadata: any;
  similarity: number;
  document_id?: string;
};

interface IntentAnalysis {
  intent: string;
  needsComprehensiveProfile: boolean;
  needsCodeExamples: boolean;
  needsProjectDetails: boolean;
  detectedTechnology?: string;
}

// Simple intent detection (no over-engineering)
function detectQueryIntent(query: string): IntentAnalysis {
  const lowerQuery = query.toLowerCase();

  const isRoleFit = /\b(fit|suitable|qualified|role|position|job|candidate|hire|assess|evaluation|match|requirements|skills gap|suitability)\b/.test(lowerQuery);
  const isCodeInquiry = /\b(code|implementation|how to|show me|example|technical|algorithm|sql|python|golang|streaming|javascript)\b/.test(lowerQuery);
  const isProjectInquiry = /\b(project|built|worked on|developed|created|gengo|clarity|streaming pipeline)\b/.test(lowerQuery);

  // Detect technology
  let detectedTechnology: string | undefined;
  if (/\b(sql|query|database|select|insert|update|delete|join)\b/.test(lowerQuery)) {
    detectedTechnology = 'sql';
  } else if (/\b(python|pandas|numpy|dataframe|jupyter|py)\b/.test(lowerQuery)) {
    detectedTechnology = 'python';
  } else if (/\b(go|golang|cli|goroutine|channel)\b/.test(lowerQuery)) {
    detectedTechnology = 'golang';
  } else if (/\b(kafka|streaming|pipeline|real-time|event)\b/.test(lowerQuery)) {
    detectedTechnology = 'streaming';
  } else if (/\b(javascript|js|node|react|typescript|ts)\b/.test(lowerQuery)) {
    detectedTechnology = 'javascript';
  }

  return {
    intent: isRoleFit ? 'role_fit' : isCodeInquiry ? 'technical_inquiry' : isProjectInquiry ? 'specific_project' : 'general_profile',
    needsComprehensiveProfile: isRoleFit,
    needsCodeExamples: isCodeInquiry,
    needsProjectDetails: isProjectInquiry,
    detectedTechnology
  };
}

// Core function: Generate query embedding using Gemini
async function generateQueryEmbedding(query: string): Promise<number[]> {
  try {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("API key not found");
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "text-embedding-004" });

    const result = await model.embedContent(query);
    return result.embedding.values;
  } catch (error) {
    console.error("Error generating embedding:", error);
    throw error;
  }
}

// Core function: Vector similarity search
async function vectorSearch(query: string, k: number = 15): Promise<DocumentResult[]> {
  try {
    console.log("🔍 Starting vector search for:", query);

    // First check if embeddings exist
    const { data: embeddingCheck, error: checkError } = await supabase
      .from('document_embeddings')
      .select('embedding')
      .not('embedding', 'is', null)
      .limit(1);

    if (checkError) {
      console.error("❌ Error checking embeddings:", checkError);
      console.log("⚠️ Falling back to semantic similarity search");
      return await fallbackSemanticSearch(query, k);
    }

    if (!embeddingCheck || embeddingCheck.length === 0) {
      console.warn("⚠️ No embeddings found in database");
      console.log("⚠️ Falling back to semantic similarity search");
      return await fallbackSemanticSearch(query, k);
    }

    // Generate query embedding
    const queryEmbedding = await generateQueryEmbedding(query);
    console.log("✅ Generated query embedding");

    // Use Supabase's vector similarity search
    const { data, error } = await supabase.rpc('match_documents', {
      query_embedding: queryEmbedding,
      match_threshold: 0.1,
      match_count: k
    });

    if (error) {
      console.error("❌ Vector search RPC error:", error);
      console.log("⚠️ Falling back to semantic similarity search");
      return await fallbackSemanticSearch(query, k);
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
      console.warn("⚠️ No vector search results found");
      console.log("⚠️ Falling back to semantic similarity search");
      return await fallbackSemanticSearch(query, k);
    }

    const results: DocumentResult[] = data.map((row: any) => ({
      content: row.content,
      metadata: row.metadata,
      similarity: row.similarity,
      document_id: row.document_id
    }));

    console.log(`📊 Vector search found ${results.length} documents`);
    return results;

  } catch (error) {
    console.error("❌ Vector search failed:", error);
    console.log("⚠️ Falling back to semantic similarity search");
    return await fallbackSemanticSearch(query, k);
  }
}

// Enhanced fallback with semantic similarity using text matching
async function fallbackSemanticSearch(query: string, k: number): Promise<DocumentResult[]> {
  console.log("🔄 Using semantic similarity fallback");

  try {
    // Extract keywords from query
    const keywords = extractKeywords(query);
    console.log("🔑 Extracted keywords:", keywords);

    // Multi-strategy search: exact match, partial match, and fuzzy match
    const strategies = [
      // Strategy 1: Exact phrase match
      async () => {
        const { data, error } = await supabase
          .from('document_embeddings')
          .select('content, metadata, document_id')
          .ilike('content', `%${query}%`)
          .limit(Math.ceil(k / 3));

        return error ? [] : data.map(row => ({
          ...row,
          similarity: 0.9 // High similarity for exact matches
        }));
      },

      // Strategy 2: Keyword matching
      async () => {
        if (keywords.length === 0) return [];

        const { data, error } = await supabase
          .from('document_embeddings')
          .select('content, metadata, document_id')
          .textSearch('content', keywords.join(' | '))
          .limit(Math.ceil(k / 2));

        return error ? [] : data.map(row => ({
          ...row,
          similarity: calculateKeywordSimilarity(row.content, keywords)
        }));
      },

      // Strategy 3: Individual keyword search
      async () => {
        const results = [];
        for (const keyword of keywords.slice(0, 3)) { // Limit to avoid too many queries
          const { data, error } = await supabase
            .from('document_embeddings')
            .select('content, metadata, document_id')
            .ilike('content', `%${keyword}%`)
            .limit(5);

          if (!error && data) {
            results.push(...data.map(row => ({
              ...row,
              similarity: 0.6 * (keyword.length / query.length) // Weight by keyword importance
            })));
          }
        }
        return results;
      }
    ];

    // Execute all strategies
    const allResults = [];
    for (const strategy of strategies) {
      try {
        const results = await strategy();
        allResults.push(...results);
      } catch (error) {
        console.warn("Strategy failed:", error);
      }
    }

    // Deduplicate and merge results
    const uniqueResults = new Map();
    allResults.forEach(result => {
      const key = result.document_id || result.content.substring(0, 100);
      if (!uniqueResults.has(key) || uniqueResults.get(key).similarity < result.similarity) {
        uniqueResults.set(key, result);
      }
    });

    const finalResults = Array.from(uniqueResults.values())
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, k)
      .map(row => ({
        content: row.content,
        metadata: row.metadata,
        similarity: row.similarity,
        document_id: row.document_id
      }));

    console.log(`📊 Semantic search found ${finalResults.length} documents`);
    return finalResults;

  } catch (error) {
    console.error("❌ Semantic search failed:", error);

    // Last resort: simple text search
    const { data, error: lastError } = await supabase
      .from('document_embeddings')
      .select('content, metadata, document_id')
      .limit(k);

    if (lastError || !data) {
      console.error("❌ All search methods failed");
      return [];
    }

    return data.map(row => ({
      content: row.content,
      metadata: row.metadata,
      similarity: 0.3, // Low similarity for fallback
      document_id: row.document_id
    }));
  }
}

// Helper function to extract meaningful keywords
function extractKeywords(query: string): string[] {
  const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'under', 'between', 'among', 'what', 'where', 'when', 'why', 'how', 'show', 'me', 'some', 'can', 'you', 'do', 'does', 'is', 'are', 'was', 'were', 'have', 'has', 'had']);

  return query
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 10); // Limit keywords
}

// Helper function to calculate keyword-based similarity
function calculateKeywordSimilarity(content: string, keywords: string[]): number {
  const contentLower = content.toLowerCase();
  let matches = 0;
  let totalKeywords = keywords.length;

  for (const keyword of keywords) {
    if (contentLower.includes(keyword.toLowerCase())) {
      matches++;
    }
  }

  return Math.min(0.8, 0.4 + (matches / totalKeywords) * 0.4);
}

// Enhanced database check with embedding verification
async function checkDatabaseContents() {
  try {
    console.log("🔍 Checking database contents...");

    // Check if table exists and has data
    const { data: tableInfo, error: tableError } = await supabase
      .from('document_embeddings')
      .select('document_id')
      .limit(1);

    if (tableError) {
      console.error("❌ Database table error:", tableError);
      return { hasTable: false, hasData: false, hasEmbeddings: false };
    }

    const hasData = tableInfo && tableInfo.length > 0;
    console.log(`📚 Table exists: ${hasData ? 'Yes' : 'No'}`);

    if (!hasData) {
      return { hasTable: true, hasData: false, hasEmbeddings: false };
    }

    // Check total document count
    const { count, error: countError } = await supabase
      .from('document_embeddings')
      .select('*', { count: 'exact', head: true });

    if (!countError) {
      console.log(`📊 Total documents: ${count}`);
    }

    // Check if embeddings column exists and has data
    const { data: embeddingData, error: embError } = await supabase
      .from('document_embeddings')
      .select('embedding')
      .not('embedding', 'is', null)
      .limit(1);

    const hasEmbeddings = !embError && embeddingData && embeddingData.length > 0;
    console.log(`🔢 Has embeddings: ${hasEmbeddings ? 'Yes' : 'No'}`);

    if (embError) {
      console.error("❌ Embedding check error:", embError);
    }

    // Check embedding dimensions if available
    if (hasEmbeddings && embeddingData[0].embedding) {
      const dimensions = Array.isArray(embeddingData[0].embedding)
        ? embeddingData[0].embedding.length
        : 0;
      console.log(`📐 Embedding dimensions: ${dimensions}`);
    }

    return {
      hasTable: true,
      hasData,
      hasEmbeddings,
      totalCount: count || 0
    };

  } catch (error) {
    console.error("❌ Database check failed:", error);
    return { hasTable: false, hasData: false, hasEmbeddings: false };
  }
}

// Enhanced retrieval with technology-aware filtering
async function enhancedVectorSearch(query: string, intentAnalysis: IntentAnalysis, k: number = 15): Promise<DocumentResult[]> {
  console.log("🎯 Enhanced vector search with intent:", intentAnalysis.intent);

  // Get base results from vector search
  let results = await vectorSearch(query, k * 2); // Get more results to filter

  // Technology-aware filtering
  if (intentAnalysis.detectedTechnology && results.length > 0) {
    const techFiltered = results.filter(doc => {
      const filename = doc.metadata?.filename?.toLowerCase() || '';
      const path = doc.metadata?.relative_path?.toLowerCase() || '';
      const content = doc.content.toLowerCase();

      switch (intentAnalysis.detectedTechnology) {
        case 'sql':
          return filename.includes('.sql') || content.includes('select') || content.includes('database');
        case 'python':
          return filename.includes('.py') || filename.includes('.ipynb') || content.includes('import ') || content.includes('def ');
        case 'golang':
          return filename.includes('.go') || content.includes('func ') || content.includes('package ');
        case 'streaming':
          return content.includes('kafka') || content.includes('streaming') || path.includes('streaming');
        case 'javascript':
          return filename.match(/\.(js|ts|jsx|tsx)$/) || content.includes('function') || content.includes('const ');
        default:
          return true;
      }
    });

    // Use filtered results if we have enough, otherwise use original
    if (techFiltered.length >= Math.min(5, k)) {
      results = techFiltered;
      console.log(`🔧 Technology filtering: ${techFiltered.length} relevant documents`);
    }
  }

  // Intent-based boosting
  if (intentAnalysis.needsComprehensiveProfile) {
    // Boost profile documents
    results.forEach(doc => {
      const filename = doc.metadata?.filename || '';
      if (['identity-and-contact.json', 'technical-skills.json', 'work-experience.json',
        'projects-showcase.json', 'career-summary.json'].includes(filename)) {
        doc.similarity += 0.1;
      }
    });
  }

  // Sort by similarity and return top k
  return results
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, k);
}

// Simplified context formatting
function formatContext(docs: DocumentResult[]): string {
  if (docs.length === 0) return "";

  return docs.map((doc, idx) => {
    const source = doc.metadata?.filename || doc.metadata?.relative_path || `Document ${idx + 1}`;
    return `### ${source}\n${doc.content}\n---`;
  }).join("\n\n");
}

// Simplified system instruction
function createSystemInstruction(context: string, intentAnalysis: IntentAnalysis): string {
  const baseInstruction = `You are an AI assistant representing Maksym Ionutsa - an engineer and linguist transitioning from localization QA in GameDev to data engineering/analytics roles.

You have access to comprehensive information about Maksym through the provided context. All information is from his public portfolio and meant to be shared.`;

  const intentInstructions = {
    role_fit: "When presented with job description, requirements, and experience, analyze job requirements against Maksym's experience. Do not present any code examples, unless asked to. Structure as: Executive Summary, Requirements Analysis, Technical Skills Match, Experience Alignment, Growth Areas, Overall Assessment. Feel free to rate on a scale of 1 to 10",
    technical_inquiry: "Find and display relevant code examples. Explain purpose, implementation, and architecture. Quote code using markdown blocks.",
    specific_project: "Deep dive into specific projects. Highlight technical challenges, solutions, outcomes, and relevant code.",
    general_profile: "Provide comprehensive overview of background, professional journey, skills, achievements, and projects."
  };

  return `${baseInstruction}

**MODE:** ${intentInstructions[intentAnalysis.intent] || intentInstructions.general_profile}

**CONTEXT:**
${context}

**GUIDELINES:**
- Base responses on provided context
- Speak naturally (don't mention "documents" or "context")  
- Provide specific examples when available
- Show code examples when relevant
- Use clear formatting with markdown`;
}

// Main chat function - DRAMATICALLY SIMPLIFIED
export async function chatWithGemini(messages: Array<{ role: string, content: string }>) {
  try {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Server configuration error: API key not found.");
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const query = messages[messages.length - 1].content;

    console.log("🔍 Processing query:", query);

    // Simple intent detection
    const intentAnalysis = detectQueryIntent(query);
    console.log("🎯 Intent:", intentAnalysis.intent, "Tech:", intentAnalysis.detectedTechnology);

    // Vector search with intent-aware enhancement
    const k = intentAnalysis.needsComprehensiveProfile ? 20 : 15;
    const retrievedDocs = await enhancedVectorSearch(query, intentAnalysis, k);

    console.log(`📄 Retrieved ${retrievedDocs.length} documents with avg similarity: ${retrievedDocs.length > 0 ? (retrievedDocs.reduce((sum, doc) => sum + doc.similarity, 0) / retrievedDocs.length).toFixed(3) : 0
      }`);

    // Format context and create system instruction
    const context = formatContext(retrievedDocs);
    const systemInstruction = createSystemInstruction(context, intentAnalysis);

    // Generate response
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      systemInstruction: { role: "model", parts: [{ text: systemInstruction }] },
      generationConfig: { temperature: 0.3 }
    });

    const history = messages.slice(0, -1).map(msg => ({
      role: msg.role === "assistant" ? "model" : "user",
      parts: [{ text: msg.content }]
    }));

    const result = await model.generateContent({
      contents: [...history, { role: "user", parts: [{ text: query }] }]
    });

    const responseText = result.response.text();
    console.log("✅ Response generated successfully");

    return responseText;

  } catch (error: any) {
    console.error("Error generating response:", error);
    if (error.message?.includes("API key not valid")) {
      throw new Error("Invalid API Key for Google Gemini.");
    }
    throw new Error("Failed to generate response from Gemini.");
  }
}

// Test function to verify vector search
async function testVectorSearch() {
  try {
    console.log("🧪 Testing vector search functionality...");

    // First check database contents
    await checkDatabaseContents();

    // Test query
    const testQuery = "Show me some Python code examples";
    console.log("\n🔍 Testing with query:", testQuery);

    // Generate embedding
    const queryEmbedding = await generateQueryEmbedding(testQuery);
    console.log("✅ Generated query embedding");

    // Test vector search
    const results = await vectorSearch(testQuery, 5);
    console.log(`\n📊 Found ${results.length} results`);

    // Log results
    results.forEach((doc, idx) => {
      console.log(`\nResult ${idx + 1}:`);
      console.log(`Similarity: ${doc.similarity.toFixed(3)}`);
      console.log(`Source: ${doc.metadata?.filename || doc.metadata?.relative_path || 'Unknown'}`);
      console.log(`Content preview: ${doc.content.substring(0, 100)}...`);
    });

    return results;
  } catch (error) {
    console.error("❌ Vector search test failed:", error);
    throw error;
  }
}
