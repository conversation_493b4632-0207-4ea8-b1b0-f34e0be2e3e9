
-- Update the document_embeddings table to use proper vector type instead of string
-- First, we'll add a new column with the proper vector type
ALTER TABLE public.document_embeddings 
ADD COLUMN embedding_vector vector(384);

-- Update existing records to convert string embeddings to proper vector format
UPDATE public.document_embeddings 
SET embedding_vector = embedding::vector
WHERE embedding IS NOT NULL;

-- Drop the old string-based embedding column
ALTER TABLE public.document_embeddings 
DROP COLUMN embedding;

-- Rename the new column to replace the old one
ALTER TABLE public.document_embeddings 
RENAME COLUMN embedding_vector TO embedding;

-- Create a more efficient index for vector similarity search using HNSW
DROP INDEX IF EXISTS document_embeddings_embedding_idx;
CREATE INDEX document_embeddings_embedding_hnsw_idx 
ON public.document_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Create a table for caching frequently queried embeddings
CREATE TABLE public.query_embeddings_cache (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  query_hash TEXT NOT NULL UNIQUE,
  query_text TEXT NOT NULL,
  embedding vector(384) NOT NULL,
  access_count INTEGER DEFAULT 1,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for fast query lookup
CREATE INDEX idx_query_embeddings_cache_hash ON public.query_embeddings_cache (query_hash);
CREATE INDEX idx_query_embeddings_cache_accessed ON public.query_embeddings_cache (last_accessed DESC);

-- Enable RLS for the cache table
ALTER TABLE public.query_embeddings_cache ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (since this is a personal site)
CREATE POLICY "Public read access for query cache" ON public.query_embeddings_cache FOR SELECT USING (true);
CREATE POLICY "Public insert access for query cache" ON public.query_embeddings_cache FOR INSERT WITH CHECK (true);
CREATE POLICY "Public update access for query cache" ON public.query_embeddings_cache FOR UPDATE USING (true);

-- Create a function to perform efficient vector similarity search
CREATE OR REPLACE FUNCTION public.search_similar_documents(
  query_embedding vector(384),
  similarity_threshold float DEFAULT 0.3,
  match_limit int DEFAULT 5
)
RETURNS TABLE (
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE sql
STABLE
AS $$
  SELECT 
    content,
    metadata,
    1 - (embedding <=> query_embedding) as similarity
  FROM document_embeddings
  WHERE 1 - (embedding <=> query_embedding) > similarity_threshold
  ORDER BY embedding <=> query_embedding
  LIMIT match_limit;
$$;

-- Create a function to cache and retrieve query embeddings
CREATE OR REPLACE FUNCTION public.get_or_cache_query_embedding(
  query_text text,
  query_hash text,
  embedding_vector vector(384) DEFAULT NULL
)
RETURNS vector(384)
LANGUAGE plpgsql
AS $$
DECLARE
  cached_embedding vector(384);
BEGIN
  -- Try to get from cache first
  SELECT embedding INTO cached_embedding
  FROM query_embeddings_cache
  WHERE query_hash = get_or_cache_query_embedding.query_hash;
  
  IF cached_embedding IS NOT NULL THEN
    -- Update access count and timestamp
    UPDATE query_embeddings_cache
    SET access_count = access_count + 1,
        last_accessed = now()
    WHERE query_hash = get_or_cache_query_embedding.query_hash;
    
    RETURN cached_embedding;
  END IF;
  
  -- If not in cache and embedding provided, store it
  IF embedding_vector IS NOT NULL THEN
    INSERT INTO query_embeddings_cache (query_hash, query_text, embedding)
    VALUES (query_hash, query_text, embedding_vector)
    ON CONFLICT (query_hash) DO UPDATE SET
      access_count = query_embeddings_cache.access_count + 1,
      last_accessed = now();
    
    RETURN embedding_vector;
  END IF;
  
  -- Return NULL if no embedding found or provided
  RETURN NULL;
END;
$$;

-- Create a cleanup function for old cache entries (optional, for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_query_cache(
  days_old int DEFAULT 30,
  min_access_count int DEFAULT 2
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
  deleted_count int;
BEGIN
  DELETE FROM query_embeddings_cache
  WHERE last_accessed < now() - (days_old || ' days')::interval
    AND access_count < min_access_count;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;
