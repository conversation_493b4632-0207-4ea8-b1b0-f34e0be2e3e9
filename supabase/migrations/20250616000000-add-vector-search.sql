-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Add embedding column to document_embeddings table if it doesn't exist
ALTER TABLE document_embeddings 
ADD COLUMN IF NOT EXISTS embedding vector(768);

-- Create the match_documents function for vector similarity search
CREATE OR REPLACE FUNCTION match_documents(
  query_embedding vector(768),
  match_threshold float DEFAULT 0.1,
  match_count int DEFAULT 15
)
RETURNS TABLE (
  document_id text,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE sql
AS $$
  SELECT
    document_id,
    content,
    metadata,
    1 - (embedding <=> query_embedding) as similarity
  FROM document_embeddings
  WHERE 1 - (embedding <=> query_embedding) > match_threshold
  ORDER BY embedding <=> query_embedding
  LIMIT match_count;
$$;

-- Create an index on the embedding column for faster similarity searches
CREATE INDEX IF NOT EXISTS document_embeddings_embedding_idx 
ON document_embeddings 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Add a comment to explain the function
COMMENT ON FUNCTION match_documents IS 'Performs vector similarity search on document embeddings using cosine similarity'; 