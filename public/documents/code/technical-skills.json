{"skills_profile": {"core_technical": [{"skill": "Python", "proficiency": "Advanced", "context": "Data pipelines, AI/ML, automation, API integration", "years_experience": "2+ years across professional and project work", "quantified_achievements": "Built automation tools reducing manual errors by 100%, developed data synchronization tools"}, {"skill": "SQL", "proficiency": "Advanced", "context": "Complex queries, database optimization, data analysis across Postgres, MySQL, DuckDB", "years_experience": "3+ years in production environments", "quantified_achievements": "Reduced in-game text discrepancies by 20% through systematic data validation"}, {"skill": "Golang", "proficiency": "Intermediate", "context": "CLI tools, performance-critical applications", "years_experience": "1+ year, demonstrated in Gengo project", "quantified_achievements": "Built production CLI tool outperforming Python prototype by 10x"}, {"skill": "Data Orchestration", "proficiency": "Advanced", "tools": ["Airflow", "<PERSON><PERSON><PERSON>"], "context": "Building and managing ETL/ELT workflows"}, {"skill": "Data Streaming", "proficiency": "Intermediate", "tools": ["Kafka"], "context": "Real-time data ingestion and processing", "quantified_achievements": "Designed Kafka streaming architecture handling 30k events/sec"}, {"skill": "Cloud Platforms", "proficiency": "Intermediate", "tools": ["AWS"], "context": "Leveraging cloud services for scalable data solutions"}, {"skill": "Data Modeling & Warehousing", "proficiency": "Intermediate-Advanced", "tools": ["dbt", "Dimensional modeling", "Medallion architecture"], "context": "Structured data transformation and analytics"}], "specialized_domains": [{"domain": "Data Engineering", "strengths": ["Pipeline architecture", "ETL/ELT design", "Data quality", "Workflow orchestration"], "experience_type": "Projects + intensive bootcamp training"}, {"domain": "Localization Engineering", "strengths": ["Process automation", "Quality systems", "International data handling", "Stakeholder management"], "experience_type": "4+ years professional experience"}, {"domain": "Leadership", "strengths": ["Team management", "Process optimization", "Cross-functional collaboration", "KPI development"], "experience_type": "3+ years leading teams", "quantified_achievements": "Led 6-member team achieving 30% quality improvement and 20% productivity gain"}], "language_proficiencies": [{"language": "English", "level": "Fluent (C2)"}, {"language": "Spanish", "level": "Fluent (C2)"}, {"language": "Ukrainian", "level": "Native"}, {"language": "Russian", "level": "Native"}, {"language": "Basque", "level": "Conversational (B1)"}]}, "searchable_keywords": ["python data pipelines", "sql optimization", "golang cli", "airflow dagster", "kafka streaming", "aws cloud", "dbt modeling", "team leadership", "multilingual", "data quality", "etl elt", "real-time processing"]}