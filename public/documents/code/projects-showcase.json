{"project_showcase": [{"name": "AI Email and Orders Processing System", "tech_stack": ["Python", "OpenAI API", "Langchain", "RAG", "Google Sheets API"], "github_link": "https://github.com/peekknuf/Generative-AI-emails", "description": "Engineered intelligent system for automated email classification and order processing with RAG-enhanced contextual understanding.", "engineering_highlights": ["Designed scalable architecture for email processing workflows", "Implemented RAG techniques for enhanced context retention", "Built robust API integrations with error handling and monitoring"], "business_impact": "Eliminated manual email processing overhead, demonstrating ability to automate complex business workflows", "code_demonstrates": ["Python API integration", "RAG implementation", "Email processing automation", "Google Sheets integration"]}, {"name": "Real-Time InfoSec Streaming Pipeline", "tech_stack": ["Python", "Kafka", "PostgreSQL", "<PERSON><PERSON>", "<PERSON>er"], "github_link": "https://github.com/peekknuf/streaming_pipeline", "description": "Architected end-to-end streaming pipeline for security log ingestion, processing, and real-time alerting.", "engineering_highlights": ["Designed Kafka streaming architecture with moderate load of 30k events/sec", "Implemented real-time data processing with monitoring", "Built comprehensive observability with Grafana dashboards"], "technical_depth": "Demonstrates expertise in stream processing, real-time systems, and production monitoring", "code_demonstrates": ["Kafka streaming", "Python data processing", "PostgreSQL integration", "Real-time monitoring", "Docker containerization"]}, {"name": "Gengo: High-Performance Relational Data Generator", "tech_stack": ["Golang", "CLI", "Concurrent processing", "Pa<PERSON><PERSON>", "CSV", "JSON Lines"], "github_link": "https://github.com/peekknuf/Gengo", "description": "Fast CLI-based data generation tool creating large-scale synthetic relational datasets in normalized 3NF e-commerce schema.", "engineering_highlights": ["Optimized for performance using Go's concurrency model for millions of rows", "Implements 3NF relational model with foreign key constraints", "Supports weighted sampling for realistic fact table generation", "Multiple output formats (CSV, JSON Lines, Parquet) with Snappy compression", "Interactive CLI interface with automatic file naming per table"], "performance_metrics": "10x performance improvement over Python prototype", "code_demonstrates": ["Golang concurrency", "CLI development", "Database schema design", "Performance optimization", "Data generation algorithms"]}, {"name": "Log Parser with TUI/CLI", "tech_stack": ["Python", "CLI", "TUI (Textual)", "Real-time Monitoring"], "github_link": "https://github.com/peekknuf/Clarity-Log-Parsing-CLI", "description": "Python tool for analyzing connection logs with dual-interface architecture supporting both interactive TUI and scriptable CLI modes.", "engineering_highlights": ["Dual-interface architecture (TUI + CLI)", "Real-time stream processing with directory monitoring", "Batch processing with flexible time range filtering", "Responsive terminal interface with keyboard navigation", "Modular codebase for maintainability"], "code_demonstrates": ["Python CLI development", "TUI with Textual library", "Real-time file monitoring", "Log parsing algorithms", "Modular architecture"]}, {"name": "Vendor Spend Analysis & Optimization", "tech_stack": ["Google Apps Script", "Google Sheets", "Gemini API", "AI/ML Integration"], "github_link": "https://github.com/peekknuf/VendorAnalysis", "description": "AI-powered vendor analysis workflow for ~400 vendors with strategic recommendations using automation.", "engineering_highlights": ["Automated workflow with Google Apps Script", "Gemini API integration with error handling and rate limiting", "Multi-phase processing pipeline", "Structured JSON output parsing", "Custom spreadsheet functions and menus"], "business_impact": "Automated complex vendor analysis providing quantified cost-saving recommendations", "code_demonstrates": ["Google Apps Script", "AI API integration", "Data processing pipelines", "Error handling", "Business automation"]}], "searchable_keywords": ["python projects", "golang cli tools", "kafka streaming", "ai integration", "rag implementation", "data generation", "log parsing", "automation scripts", "api integration", "real-time processing", "performance optimization", "docker containers", "database design", "tui interfaces"]}